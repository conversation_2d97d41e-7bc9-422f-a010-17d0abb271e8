# -*- coding: utf-8 -*-
# @Time   : 2025/03/18 14:30
# <AUTHOR> 和森
# @Email  : <EMAIL>
# @File   : ai_cr.py
# @Project: app-service
# @Desc   : 主要实现了使用大模型进行CR建议的功能

import sys
import requests
import json
import fnmatch
from llama_index.llms.openai import OpenAI
from llama_index.core.llms import ChatMessage
from llama_index.core.prompts import PromptTemplate

# 定义需要过滤的文件类型 (匹配模式)
IGNORE_PATTERNS = [
    "vendor/**",    # 忽略 vendor 目录下的所有文件
    "*.pb.go",      # 忽略以 .pb.go 结尾的文件
    "*.pb.dart",    # 忽略以 .pb.dart 结尾的文件
    "*.proto",      # 忽略 .proto 文件
    "*.md",         # 忽略 Markdown 文件
    "*.yaml",       # 忽略 YAML 文件
    "*.toml",       # 忽略 TOML 文件
    "*.txt",        # 忽略 TXT 文件
    "*.json",       # 忽略 JSON 文件
    "*.ini",        # 忽略 INI 文件
    "*.pyc",        # 忽略 Python 编译文件
    "*.log",        # 忽略日志文件
    "*.tmp",        # 忽略临时文件
    "*.swp",        # 忽略交换文件
    "*.xml",        # 忽略 XML 文件
]

openai_api_key = "oL89EFxmJMXVCrePQDuHP/IQp3hS4U/19A73uyKZKdOjTRR7vl/KnVhLvEEm3ZQuHyXrF3VuYyaP"
llm_model = "default/qwen3-235b-a22b"
# llm_model = "default/deepseek-v3-0324"

def get_mr_changes(project_id, iid, private_token):
    """
    调用 GitLab API 获取指定合并请求 (MR) 的变更文件信息

    :param project_id: GitLab 项目的 ID
    :param iid: Merge Request (MR) 的内部 ID
    :param private_token: GitLab API 访问的私有 Token
    :return: 包含 MR 变更信息的 JSON 数据
    """
    url = f"https://gitlab.gwm.cn/api/v4/projects/{project_id}/merge_requests/{iid}/changes"
    headers = {"PRIVATE-TOKEN": private_token}

    try:
        res = requests.get(url, headers=headers)
        res.raise_for_status()  # 确保 HTTP 请求成功
        return res.json()
    except requests.RequestException as e:
        print(f"获取 MR 变更失败: {e}")
        return {"changes": []}  # 避免返回 None 造成异常


def filter_files(file_list):
    """过滤掉匹配 IGNORE_PATTERNS 规则的文件"""
    filtered_files = []
    for file in file_list:
        if any(fnmatch.fnmatch(file, pattern) for pattern in IGNORE_PATTERNS):
            continue
        filtered_files.append(file)
    return filtered_files


def filter_mr_changes(project_id, iid, private_token):
    """
    获取 MR 变更信息，并过滤掉 IGNORE_PATTERNS 中的文件类型

    :param project_id: GitLab 项目的 ID
    :param iid: Merge Request (MR) 的内部 ID
    :param private_token: GitLab API 访问的私有 Token
    :return: 过滤后的变更文件列表，每个文件包含 new_path 和 diff
    """

    res = get_mr_changes(project_id, iid, private_token)
    # print(res)
    changes = res.get("changes", [])

    # 处理 changes 为空的情况，直接返回空列
    if not changes:
        print("mr未发现文件变更")
        return []

    filtered_changes = []
    for change in changes:
        new_path = change.get("new_path", "")
        diff = change.get("diff", "")

        # 过滤掉匹配 IGNORE_PATTERNS 的文件
        if any(fnmatch.fnmatch(new_path, pattern) for pattern in IGNORE_PATTERNS):
            continue

        filtered_changes.append({"new_path": new_path, "diff": diff})

    return filtered_changes


def review_mr_changes(project_id, iid, private_token):
    """
    评审 MR 代码变更，调用大模型生成评审意见

    :param project_id: GitLab 项目的 ID
    :param iid: Merge Request 的 ID
    :return: 大模型生成的代码评审意见
    """

    filtered_changes = filter_mr_changes(project_id, iid, private_token)
    if not filtered_changes:
        return "没有需要评审的代码变更"

    # 定义 PromptTemplate，强调 git diff 解析
    prompt_template = PromptTemplate(
        """你是一名资深软件工程师，专注于代码的规范性、功能性、安全性和稳定性。本次任务需要对merge request中的代码变更进行评审。请根据以下代码变更，提供详细的评审意见。
        
## **代码审查目标**
你需要分析 Git diff 结构的代码变更，并提供详细的 Code Review 反馈，具体包括：
- **代码逻辑**: 是否有潜在的逻辑错误或边界情况未考虑 （40分）
- **代码风格**: 是否符合编码规范，是否可以优化 （20分）
- **性能优化**: 是否有低效的代码或可优化的部分 （20分）
- **安全性**: 是否存在安全隐患，如 SQL 注入、XSS、未验证输入等（10分）
- **代码可读性**: 是否能提高代码的可维护性 （10分）

## **Git Diff 说明**
变更的代码是 `git diff` 格式，结构如下：
- `@@ -old_start,old_length +new_start,new_length @@` 表示变更范围
- `-` 开头的行表示被删除的代码
- `+` 开头的行表示新增的代码
- 其他行是上下文，帮助理解代码逻辑

## **代码变更**
{changes}

## **评审要求**
请依据上述代码变更：
1.问题描述和优化建议(如果有)：列出代码中存在的问题，简要说明其影响，并务必给出优化建议和示例代码。
2.务必对每条建议标注代码文件名以及代码行数（范围）。
3.评分明细：为每个评分标准提供具体分数，并以markdown表格的形式展示。
4.总分：格式为“总分:XX分”（例如：总分:80分），确保可通过正则表达式 r"总分[:：]\s*(\d+)分?"） 解析出总分。
5.输出格式应清晰、结构化，便于开发者阅读和修正
6.最后总结的部分，请按照问题或建议的优先级排序。

请生成 Code Review 反馈：
"""
    )

    # 格式化 changes 内容
    changes_str = "\n\n".join(
        [f"### 文件: {change['new_path']}\n```diff\n{change['diff']}\n```" for change in filtered_changes]
    )

    # 使用 PromptTemplate 生成最终的 prompt
    review_prompt = prompt_template.format(changes=changes_str)
    print(review_prompt)

    # 调用 LlamaIndex 中的大模型
    llm = OpenAI(
        api_key= openai_api_key,
        api_base="http://llmproxy.gwm.cn/v1",
        default_headers={"model": llm_model}
    )

    messages = [ChatMessage(role="user", content=review_prompt)]
    response = llm.chat(messages=messages)

    return response


def llm_test():
    llm = OpenAI(
        api_key=openai_api_key,
        api_base="http://llmproxy.gwm.cn/v1",
        default_headers={"model": llm_model}
    )
    messages = [ChatMessage(role="user", content="你好，我是小明")]
    response = llm.chat(messages=messages)
    print(response)
    print(type(response), dir(response))
    m = response.message
    print(type(m), m)
    t = str(m)
    print(type(t), t)



if __name__ == '__main__':
    # GitLab 项目 ID 和 MR ID 示例
    PROJECT_ID = 4142
    MR_IID = 263
    PRIVATE_TOKEN = "Jgw7UPaPZuyAGZb2CdqC"  # 需替换为有效的 GitLab API Token

    # changes = get_mr_changes(4142, 263)
    # print(changes.get('changes'))
    changed_files = [
        "src/main.py",
        "docs/readme.md",
        "config/settings.yaml",
        "data/schema.proto",
        "vendor/lib/dependency.txt",
        "src/module.py"
    ]

    # filtered_files = filter_files(changed_files)
    # print(filtered_files)  # 只会保留未被过滤的文件
    # final_changes = filter_mr_changes(4142, 263, private_token=PRIVATE_TOKEN)
    # print(final_changes)
    llm_test()
    # res = review_mr_changes(2609, 995, private_token=PRIVATE_TOKEN)
    # print(res)
    # print(dir(res))
    # msg = res.messages
    # print(type(msg), msg)
