# -*- coding: utf-8 -*-
# @Time   : 2025/6/5 11:16
# <AUTHOR> 王志强
# @Email  : <EMAIL>
# @File   : exceptiont.py
# @Project: ai-service
# @Desc   : 主要实现了xxx功能

from enum import IntEnum

class _BaseException(Exception):
    """base异常"""

    def __init__(self, err):
        Exception.__init__(self)
        self.err = err

    def __str__(self, ):
        return self.err


class DBExecException(_BaseException):
    """数据库执行异常"""

    def __init__(self, err):
        super(DBExecException, self).__init__(err)


class ExceptionLevelEnum(IntEnum):
    error = 1
    warning = 2
    info = 3


# 0-999 系统级别
# 0-99 未知异常

class AppException(_BaseException):
    title = '系统内部异常'
    status = 1
    status_code = 200
    level = ExceptionLevelEnum.error

    def __init__(self, message='ERROR'):
        self.message = message

    def __str__(self):
        return '[%s]%s' % (self.title, self.message)

class GitlabApiException(AppException):
    status, title = 5001, 'gitlab api 请求异常'

class SystemException(AppException):
    status, title = 8888, '内部系统错误'

class MRFileChangeException(AppException):
    status, title = 4001, 'MR 变更文件不符合要求'

class MRFetchException(AppException):
    status, title = 4002, '获取 MR 信息失败'
