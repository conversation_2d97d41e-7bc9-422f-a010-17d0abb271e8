# -*- coding: utf-8 -*-
# @Time   : 2025/6/5 11:34
# <AUTHOR> 王志强
# @Email  : <EMAIL>
# @File   : prompts.py
# @Project: ai-service
# @Desc   : 主要实现了xxx功能

# 主prompt
CODE_REVIEW_PROMPT = """你是一名资深软件工程师，专注于代码的规范性、功能性、安全性和稳定性。本次任务需要对merge request中的代码变更进行评审。请根据以下代码变更，提供详细的评审意见。
        
## **代码审查目标**
你需要分析 Git diff 结构的代码变更，并提供详细的 Code Review 反馈，具体包括：
- **代码逻辑**: 是否有潜在的逻辑错误或边界情况未考虑 （40分）
- **代码风格**: 是否符合编码规范，是否可以优化 （20分）
- **性能优化**: 是否有低效的代码或可优化的部分 （20分）
- **安全性**: 是否存在安全隐患，如 SQL 注入、XSS、未验证输入等（10分）
- **代码可读性**: 是否能提高代码的可维护性 （10分）

## **Git Diff 说明**
变更的代码是 `git diff` 格式，结构如下：
- `@@ -old_start,old_length +new_start,new_length @@` 表示变更范围
- `-` 开头的行表示被删除的代码
- `+` 开头的行表示新增的代码,并且每行新增代码后都有这一行号行号的注释，如：“env_params = json.loads(row[1])  # line 71”，表明这一行代码是新增代码的71行
- 其他行是上下文，帮助理解代码逻辑

## **代码变更**
{changes}

## **评审要求**
请依据上述代码变更，生成结构化的评审报告，整体以markdown格式返回。需严格按照以下格式与要求输出
### 1.首先简明扼要、按照序号给出功能的整体描述
### 2.问题识别与优化建议（如有）  
对代码中存在的问题，逐项指出：

- **问题描述**：概括问题本质  
- **影响分析**：说明该问题可能造成的后果  
- **优化建议**：明确的改进措施，包含可选示例代码  
- **强制要求**：每条建议**必须标注“文件名”和“行号范围”**，格式如下：

- ### 1. **问题描述**: 简洁明了地说明存在的问题
- **文件**: path/to/file.py  
- **行号**: L10-L18
- **影响**: 问题可能引发的具体影响
- **优化建议**: 明确指出改进方式
- **示例代码**:
  ```python
  # 示例代码
```

注意：
> 未标注文件名与行号的建议将视为无效，不纳入评分与建议统计。
> 行号范围必须标注为变更后新增代码的行号范围，不能是删除代码的行号范围，并且使用每行代码后的注释的行号为准。

### 3.评分明细：为每个评分标准提供具体分数，并以markdown表格的形式展示。
### 4.总分：格式为“总分:XX分”（例如：总分:80分），确保可通过正则表达式 r"总分[:：]\s*(\d+)分?"） 解析出总分。
### 5. 建议汇总（按优先级排序）  
请将所有建议按严重性/优先级进行排序，并以markdown表格格式简要列出建议摘要，格式如下：

| 优先级 | 文件路径         | 行号范围   | 问题描述                       | 建议                           |
|--------|------------------|------------|--------------------------------|--------------------------------|
| 高     | core/api.py      | L34-L38    | 存在硬编码                     | 建议提取为配置项               |
| 中     | utils/logger.py  | L15        | 打印语句使用 print             | 建议使用 logging 模块          |
| 低     | views/home.py    | L22        | 命名不规范                     | 建议按 PEP8 命名规范重命名     |

### 6. 每个部分之间以markdown分割线（'---'）隔开 

请生成 Code Review 反馈：
"""

# 代码格式子prompt
GIT_DIFF_INFO_PROMPT = """

## **Git Diff 说明**
Git diff 格式用于表示代码变更，需严格遵循以下规则解析：

### **1. Hunk 头解析**
每段变更以 `@@ -old_start,old_length +new_start,new_length @@` 开头：
- `-old_start,old_length`：原始文件中被删除/修改的行号范围
- `+new_start,new_length`：**变更后新增代码的起始行号和行数**（关键！）

> **核心规则**：  
> - 新增代码的行号范围 = `new_start` 到 `new_start + new_length - 1`  
> - 示例：`@@ -26,6 +26,7 @@` 表示新增代码从 **第26行开始，共7行**，结束于 **第32行**

---

### **2. 行号标注规则**
#### **(1) 新增代码（+前缀）**
- **必须标注为“+”行对应的变更后行号范围**  
- 示例：若 `@@ -26,6 +26,7 @@` 对应的新增代码为：
  ```diff
  +        self.env_name = self.params.get('env_name', '')
  ```
  则标注为 `L26-L32`（26为起始行号，26+7-1=32）

#### **(2) 删除代码（-前缀）**
- 仅用于理解逻辑，**评审建议不得标注删除代码的行号**  
- 示例：
  ```diff
  -        if not self.pipeline_id:
  -            return None
  ```

#### **(3) 上下文代码（无前缀）**
- 用于定位逻辑关系，**评审建议可参考但不得标注上下文行号**

---

### **3. 示例解析**
#### **输入 diff 片段**：
```diff
@@ -26,6 +26,7 @@ class StopServiceStep(DeployStep):
         self.child_process_handle = self.params.get('child_process_handle', 0)
         self.enable_systemd = self.params.get('enable_systemd', 0)
         self.pipeline_id = self.params.get('RESOURCE_ID')
+        self.env_name = self.params.get('env_name', '')
```

#### **行号标注逻辑**：
- `+new_start=26`, `new_length=7` → 行号范围：**L26-L32**
- 评审建议应标注为 `L26-L32`

#### **输出建议示例**：
```markdown
- ### 1. **问题描述**: 新增字段未做类型校验  
- **文件**: deploy/step/stopservice.py  
- **行号**: L26-L32  
- **影响**: 若传入非字符串类型可能导致后续逻辑异常  
- **优化建议**: 添加类型检查或默认值处理  
- **示例代码**:
  ```python
  self.env_name = str(self.params.get('env_name', ''))
  ```
```

---

### **4. 多 Hunk 情况处理**
若 diff 包含多个 hunk：
```diff
@@ -26,6 +26,7 @@
@@ -43,37 +44,62 @@
```
- 每个 hunk 的新增代码需**独立计算行号范围**：
  - 第1个 hunk: L26-L32  
  - 第2个 hunk: L44-L62（44+62-1=105？需根据实际行数修正）  
  - **注意**：行号范围需严格基于每个 hunk 的 `+new_start,new_length`

---

### **5. 评审建议标注要求**
- **强制要求**：每条建议必须标注 **变更后代码的行号范围**（仅含“+”行）  
- **禁止标注**：删除代码（-前缀）或上下文代码的行号  
- **格式示例**：
  - 正确：`L44-L62`（新增代码范围）
  - 错误：`L43-L62`（包含原文件删除代码）

---

### **6. 评审辅助提示**
- 若遇到复杂 diff，请优先分析 `+new_start,new_length` 确定新增代码的起始行号  
- 对于多行新增，需将所有“+”行视为连续的行号块（即使中间夹杂上下文代码）  
- 可通过 `new_start + new_length - 1` 公式自动计算结束行号

"""