import sys
sys.path.append('/Users/<USER>/ai-service') 
import re
from common.thirdparty.gitlabapi import GitLabAPI

def annotate_added_line_numbers(diff_text: str) -> str:
    lines = diff_text.splitlines(keepends=True)
    annotated_lines = []

    current_new_lineno = None
    line_offset = 0

    for line in lines:
        if line.startswith('@@'):
            match = re.search(r'\+(\d+)(?:,(\d+))?', line)
            if match:
                current_new_lineno = int(match.group(1))
                line_offset = 0
            annotated_lines.append(line)
            continue

        # 新增行（+开头，非文件标识）
        if line.startswith('+') and not line.startswith('+++'):
            lineno = current_new_lineno + line_offset
            newline = '\n'
            if line.endswith('\r\n'):
                newline = '\r\n'
            elif line.endswith('\r'):
                newline = '\r'
            content = line.rstrip('\r\n')
            annotated_lines.append(f"{content}  # line {lineno}{newline}")
            line_offset += 1

        # 删除行（-开头，非文件标识）
        elif line.startswith('-') and not line.startswith('---'):
            annotated_lines.append(line)

        # 上下文行（空格开头或空行） → 消耗新行号
        else:
            annotated_lines.append(line)
            line_offset += 1

    return ''.join(annotated_lines)



if __name__ == "__main__":
    # 从提供的changes数据中提取diff字符串
    res = GitLabAPI().get_mr_changes(4142, 306)
    changes = res.get("changes", [])
    for change in changes:
        diff = change.get("diff", "")
        # print(f"===raw diff==={diff}")
        print(f"===annotated diff===\n{annotate_added_line_numbers(diff)}")