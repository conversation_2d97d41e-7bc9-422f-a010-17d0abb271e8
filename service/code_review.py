# -*- coding: utf-8 -*-
# @Time   : 2025/03/18 14:30
# <AUTHOR> 和森
# @Email  : <EMAIL>
# @File   : ai_cr.py
# @Project: app-service
# @Desc   : 主要实现了使用大模型进行CR建议的功能

import sys
sys.path.append('/Users/<USER>/ai-service')  # 添加这一行在导入common模块之前

from common.config.config import (LLM_MODEL, LLM_API_KEY, LLM_API_URL, REVIEW_MAX_TOKENS, REVIEW_MAX_CODE_PLUS)
from common.basic.prompts import CODE_REVIEW_PROMPT, GIT_DIFF_INFO_PROMPT
from common.thirdparty.gitlabapi import GitLabAPI
from common.utils.token_util import count_tokens, truncate_text_by_tokens
from common.utils.git_diff_format import annotate_added_line_numbers
from common.basic.exception import GitlabApiException, MRFileChangeException, MRFetchException
import sys
import requests
import json
import fnmatch
from llama_index.llms.openai import OpenAI
from llama_index.core.llms import ChatMessage
from llama_index.core.prompts import PromptTemplate


# 定义指定文件类型
SPECIFIC_FILE_TYPES = [
    "*.go",  # 只处理 Go 语言文件
    "*.java",  # 只处理 Java 语言文件
    "*.py",  # 只处理 Python 语言文件
    "*.js",  # 只处理 JavaScript 语言文件
    "*.ts",  # 只处理 TypeScript 语言文件
]

def filter_files(file_list):
    """过滤掉匹配 IGNORE_PATTERNS 规则的文件"""
    filtered_files = []
    for file in file_list:
        if not any(fnmatch.fnmatch(file, pattern) for pattern in SPECIFIC_FILE_TYPES):
            continue
        filtered_files.append(file)
    return filtered_files

def filter_mr_changes(project_id, iid):
    """
    获取 MR 变更信息，并只处理 SPECIFIC_FILE_TYPES 中的文件类型
    
    :param project_id: GitLab 项目的 ID
    :param iid: Merge Request (MR) 的内部 ID
    :return: 返回字典包含:
             - data: 过滤后的变更文件列表(可能为空)
             - status: 执行状态(True/False)
             - reason: 当status为False时的原因说明
    """
    try:
        res = GitLabAPI().get_mr_changes(project_id, iid)
        changes = res.get("changes", [])

        print(f"===raw changes==:{changes}")
        
        if not changes:
            raise MRFileChangeException("MR 中没有文件变更")
           
        filtered_changes = []
        total_diff_plus = 0
        for change in changes:
            new_path = change.get("new_path", "")
            diff = change.get("diff", "")
            print("===diff===")
            print(diff)
            diff = annotate_added_line_numbers(diff)
            print("===annotated diff===")
            print(diff)
            
            # 检查是否为指定文件类型
            if not any(fnmatch.fnmatch(new_path, pattern) for pattern in SPECIFIC_FILE_TYPES):
                continue

            # 计算新增行数
            diff_plus = diff.count("\n+")
            total_diff_plus += diff_plus 
            filtered_changes.append({"new_path": new_path, "diff": diff})
            
        print(f"total_diff_plus:{total_diff_plus}")
        if not filtered_changes:
            raise MRFileChangeException("MR 中没有需要处理的代码变更(所有变更文件都不在指定类型范围内)")
        elif total_diff_plus > REVIEW_MAX_CODE_PLUS:
            # 新增变更行数超过限制，不进行AI代码评审
            raise MRFileChangeException(f"MR 中需要处理的代码新增变更行数超过{REVIEW_MAX_CODE_PLUS}，不进行AI代码评审.")

        return filtered_changes
        
    except MRFileChangeException:
        raise
    except Exception as e:
        raise MRFetchException(f"获取 MR 变更时发生错误: {str(e)}")


def review_mr_changes(project_id, iid):
    """
    评审 MR 代码变更，调用大模型生成评审意见

    :param project_id: GitLab 项目的 ID
    :param iid: Merge Request 的 ID
    :return: 大模型生成的代码评审意见，如果没有需要评审的内容则m返回原因
    """

    print(f"开始分析项目:{project_id} MR:{iid} 变更内容...")
    result = filter_mr_changes(project_id, iid)
 
    # 定义 PromptTemplate，强调 git diff 解析
    prompt_template = PromptTemplate(CODE_REVIEW_PROMPT)

    # 格式化 changes 内容
    changes_str = "\n\n".join(
        [f"### 文件: {change['new_path']}\n```diff\n{change['diff']}\n```" for change in result]
    )

    # 计算tokens数量，如果超过REVIEW_MAX_TOKENS，截断changes_text
    total_tokens = count_tokens(changes_str)
    print(f"total_tokens:{total_tokens}")

    if total_tokens > REVIEW_MAX_TOKENS:
        print(f"AI代码评审：变更内容超过最大token限制({REVIEW_MAX_TOKENS})，将进行截断。")
        changes_str = truncate_text_by_tokens(changes_str, REVIEW_MAX_TOKENS)


    # 使用 PromptTemplate 生成最终的 prompt
    review_prompt = prompt_template.format(changes=changes_str)

    print(f"review_prompt:{review_prompt}")

    print("开始调用大模型...")
    # 调用 LlamaIndex 中的大模型
    llm = OpenAI(
        api_key=LLM_API_KEY,
        api_base=LLM_API_URL,
        default_headers={"model": LLM_MODEL}
    )

    messages = [ChatMessage(role="user", content=review_prompt)]
    response = llm.chat(messages=messages)
    print("AI代码评审成功.")
    print(f"response:{response}")
    return str(response)


if __name__ == "__main__":
        changed_files = [
        "src/main.py",
        "docs/readme.md",
        "config/settings.yaml",
        "data/schema.proto",
        "vendor/lib/dependency.txt",
        "src/module.py",
        "src/module.go",
        "src/module.java",
        "src/module.js",
        "src/module.ts",
        "src/module.pyc",
    ]
        # filtered_files = filter_files(changed_files)
        # print("过滤后的文件列表:") 
        # print(filtered_files)
        # res = review_mr_changes(4142, 306)
        # res = review_mr_changes(2609, 995)
        res = review_mr_changes(6900,2693)
        # print(f"res:{res}")

        # filter_mr_changes(4142, 306) 

